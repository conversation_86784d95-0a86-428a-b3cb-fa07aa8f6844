import {
  type TAutoBuyStatusCode,
  AutoBuyService,
  type IAutoBayStoreDto,
} from "@modules/services/autoBuy";
import { useAxios } from "@/helpers";

const autoBuyService = new AutoBuyService();

/**
 * Checks if there are any existing auto-buy records and deletes the first one found.
 * @returns {Promise<void>} A promise that resolves when the operation is complete.
 *
 * @category Composables
 */
export async function checkAndDeleteAutoBay() {
  const res = await autoBuyService.showAutoBuy();
  if (res.data && res.data?.length > 0) {
    const autoBuyId = res.data[0].id;
    await autoBuyService.deleteAutoBuy(autoBuyId);
  }
}

/**
 * Check and create auto buy
 * @param {IAutoBayStoreDto} config
 * @returns {Promise<IAutoBayStoreDto>}
 * @category Composables
 */
export async function checkAndCreateAutoBuy(config: IAutoBayStoreDto) {
  console.log("DEBUG checkAndCreateAutoBuy ", config);
  debugger;

  const res = await autoBuyService.showAutoBuy();
  console.log("DEBUG showAutoBuy ", res);
  if (res.data && res.data?.length > 0) {
    const autoBuyId = res.data[0].id;
    await autoBuyService.deleteAutoBuy(autoBuyId!);
  }
  return await autoBuyService.autoBuyStore(config);
}

/**
 * Check auto buy transaction
 *
 * @param {string} accountAddress
 *
 * @category Composables
 */
export function checkAutoBuyTransaction(accountAddress: string) {
  console.log("DEBUG checkAutoBuyTransaction ", accountAddress);
  return useAxios().get(
    `https://blockchain.pst.net/last-activity/${accountAddress}`
  );
}

/**
 * Check auto buy timer
 *
 * @param {number} autoBuyId
 * @param cb callback function
 *
 * @category Composables
 */
export function checkAutoBuyTimer(
  autoBuyId: number,
  cb: (status: TAutoBuyStatusCode) => void
) {
  const timer = setInterval(async () => {
    const res = await autoBuyService.autoBuyDetail(autoBuyId);
    if (!res.status) {
      clearInterval(timer);
      return;
    }
    if (res.data?.status) {
      cb(res.data?.status);
    }
  }, 5000);

  return timer;
}
