import type {
  IAutoBuyModelService,
  IAutoBayStoreDto,
  IAutoBuyModel,
  IResponse,
} from "./model";
import type { IAutoBayStoreRes, IAutoBuyDetailRes } from "./types";
import { useAxios } from "@/helpers";
import { AUTO_BUY_API } from "@modules/services/autoBuy/api";
import type { AxiosError } from "axios";

export class AutoBuyService implements IAutoBuyModelService {
  async autoBuyDetail(id: number): Promise<IResponse<IAutoBuyDetailRes>> {
    console.log("DEBUG svc autoBuyDetail", id);

    try {
      const res = await useAxios().get(AUTO_BUY_API.DETAIL + id);
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }

  async autoBuyStore(
    config: IAutoBayStoreDto
  ): Promise<IResponse<IAutoBayStoreRes>> {
    console.log("DEBUG svc autoBuyStore", config);
    try {
      const res = await useAxios().post(AUTO_BUY_API.STORE, config);
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }

  async deleteAutoBuy(id: number): Promise<IResponse<{ success: boolean }>> {
    console.log("DEBUG svc deleteAutoBuy", id);
    try {
      const res = await useAxios().delete(AUTO_BUY_API.DELETE + id);
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }

  async showAutoBuy(): Promise<IResponse<IAutoBuyModel[]>> {
    console.log("DEBUG svc showAutoBuy");
    try {
      const res = await useAxios().get(AUTO_BUY_API.SHOW);
      return {
        status: true,
        data: res.data.data,
      };
    } catch (e) {
      return {
        status: false,
        error: e as AxiosError,
      };
    }
  }
}
