<script setup lang="ts">
import { computed, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import CreateCardAutoBuyPayment from "@/components/CreateCardAutoBuyPayment/CreateCardAutoBuyPayment.vue";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import {
  type TCardTariffSlug,
  useCardAutoRefillPost,
  useCardsGet,
} from "@/composable";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import { RouteName } from "@/constants/route_name";
import { useI18n } from "vue-i18n";
import CreateCardSuccess from "@/components/CreateCardSuccess/CreateCardSuccess.vue";

const route = useRoute();
const router = useRouter();
const { t } = useI18n();

type AutobuyParams = {
  account_id?: string;
  type: TCardTariffSlug;
  system?: string;
  code?: string;
  start_balance?: string;
};

// state: 'autobuy', 'success'
enum State {
  AUTOBUY = "autobuy",
  SUCCESS = "success",
}

const viewState = ref<State>(State.AUTOBUY);

const autobuyParams = computed<AutobuyParams>(() => {
  return {
    account_id: route.query.account_id as string,
    type: route.query.type as TCardTariffSlug,
    system: route.query.system as string,
    code: route.query.code as string,
    start_balance: route.query.start_balance as string,
  };
});

const cardForIssue = computed<TCardForIssue>(() => {
  return {
    bin: "",
    type: autobuyParams.value.type,
    startBalance: Number(autobuyParams.value.start_balance),
    minValue: 1,
    accountId: Number(autobuyParams.value.account_id),
    description: "",
    count: 1,
    system: Number(autobuyParams.value.system),
  };
});

const enableAutoRefill = async (cardId: number) => {
  await useCardAutoRefillPost({
    card_id: cardId,
    minimum_balance: "50",
    amount_refill: "50",
  });
};

const handleAutoBuy = async () => {
  viewState.value = State.SUCCESS;
  const { data: cards } = await useCardsGet();
  const resultCard = cards.value?.data?.length ? cards.value?.data[0] : null;

  if (resultCard) {
    await enableAutoRefill(resultCard.id);
  }
};

const handleSuccess = () => {
  router.push({ name: RouteName.DASHBOARD });
};

const handleClose = () => {
  router.push({ name: RouteName.DASHBOARD });
};
</script>

<template>
  <div class="proceed-autobuy-view">
    <div>
      <UIFullScreenModal
        :is-open="true"
        :title="t('create-card.modal-title')"
        :can-go-back="false"
        @close="handleClose">
        <template #content>
          <div v-if="viewState === State.AUTOBUY">
            <CreateCardAutoBuyPayment
              :card-for-issue="cardForIssue"
              :promo-code-data="null"
              @auto-buy-success="handleAutoBuy" />
          </div>
          <div v-else-if="viewState === State.SUCCESS">
            <CreateCardSuccess @confirm="handleSuccess" />
          </div>
        </template>
      </UIFullScreenModal>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
