{"name": "unstable", "version": "0.0.0", "scripts": {"dev": "vite", "dev-host": "vite --host", "build": "run-p type-check check-strings build-only", "preview": "vite preview --port 4173", "test:unit": "vitest --environment jsdom run", "test:coverage": "vitest --environment jsdom --coverage", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "translate": "node src/translate_node/translate.js", "sync-translate": "node src/translate_node/sync.js", "prepare": "husky install", "prettier": "prettier --write src/**/*.{vue,js,ts}", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "remove-unused-files": "node scripts/remove_unused_files.js", "jsdoc": "./node_modules/.bin/jsdoc -c jsdoc.config.json", "check-strings": "node scripts/check_incorrect_strings.js"}, "dependencies": {"@amplitude/analytics-browser": "1.13.4", "@amplitude/analytics-types": "0.17.1", "@fingerprintjs/fingerprintjs": "3.4.2", "@formkit/auto-animate": "0.8.1", "@intercom/messenger-js-sdk": "0.0.17", "@onesignal/onesignal-vue3": "1.0.2", "@popperjs/core": "2.11.8", "@sentry/replay": "7.106.0", "@sentry/tracing": "7.106.0", "@sentry/vue": "7.106.0", "@vueuse/components": "9.13.0", "@vueuse/core": "10.11.1", "@xstate/vue": "3.1.2", "axios": "1.6.7", "bignumber.js": "9.1.2", "chart.js": "4.4.2", "crypto-js": "4.2.0", "date-fns": "2.30.0", "floating-vue": "5.2.2", "gsap": "3.12.3", "lodash.debounce": "4.0.8", "pinia": "2.1.7", "qr-code-styling": "1.9.2", "qrcode": "1.5.3", "query-string": "8.2.0", "simplebar-vue": "2.3.3", "swiper": "8.4.7", "uuid": "^9.0.0", "v-calendar": "3.1.2", "v-mask": "2.3.0", "vee-validate": "4.10.2", "vue": "3.4.21", "vue-content-loader": "2.0.1", "vue-currency-input": "3.1.0", "vue-i18n": "9.10.1", "vue-query": "1.26.0", "vue-router": "4.3.0", "vue-select": "4.0.0-beta.6", "vue-skeletor": "1.0.6", "vue-timer-hook": "1.0.84", "vue-toastification": "2.0.0-rc.5", "vue3-cookies": "1.0.6", "xstate": "5.14.0", " ": "0.32.11"}, "devDependencies": {"@vitejs/plugin-legacy": "4.1.1", "@types/crypto-js": "4.2.2", "@rollup/plugin-dynamic-import-vars": "1.4.4", "@rushstack/eslint-patch": "1.7.2", "@sentry/types": "^7.73.0", "@sentry/vite-plugin": "^0.4.0", "@storybook/addon-essentials": "8.4.6", "@storybook/addon-interactions": "8.4.6", "@storybook/addon-links": "8.4.6", "@storybook/addon-storysource": "8.4.6", "@storybook/addon-themes": "8.4.6", "@storybook/blocks": "8.4.6", "@storybook/test": "8.4.6", "@storybook/vue3": "8.4.6", "@storybook/vue3-vite": "8.4.6", "@types/big.js": "6.2.2", "@types/iframe-resizer": "3.5.13", "@types/jsdom": "20.0.1", "@types/node": "^16.18.87", "@types/qrcode": "1.5.5", "@types/uuid": "8.3.4", "@types/vue-select": "3.16.8", "@vitejs/plugin-vue": "4.6.2", "@vitest/coverage-c8": "0.26.3", "@vue/eslint-config-prettier": "7.1.0", "@vue/eslint-config-typescript": "11.0.3", "@vue/test-utils": "2.4.4", "@vue/tsconfig": "0.1.3", "autoprefixer": "10.4.18", "better-docs": "2.7.3", "eslint": "8.57.0", "eslint-import-resolver-custom-alias": "1.3.2", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-i18n": "2.3.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-storybook": "0.11.1", "eslint-plugin-vue": "9.22.0", "fs": "0.0.1-security", "glob": "10.3.10", "husky": "8.0.3", "jsdoc": "4.0.4", "jsdom": "20.0.3", "lint-staged": "15.2.2", "mitt": "3.0.1", "npm-run-all": "4.1.5", "path": "0.12.7", "postcss": "8.4.38", "prettier": "2.8.8", "sass": "1.71.1", "storybook": "8.4.6", "tailwindcss": "3.4.1", "tailwindcss-themer": "3.1.3", "typescript": "5.5.4", "vite": "4.5.2", "vite-plugin-checker": "0.8.0", "vite-plugin-html": "3.2.2", "vite-plugin-imp": "2.4.0", "vite-plugin-vue-devtools": "7.3.4", "vite-svg-loader": "3.6.0", "vitest": "0.23.4", "vue-component-type-helpers": "1.8.27", "vue-tsc": "2.1.6", "vue3-portal": "2.0.2"}, "lint-staged": {"src/**/*.{js,ts,vue}": "eslint --fix"}}